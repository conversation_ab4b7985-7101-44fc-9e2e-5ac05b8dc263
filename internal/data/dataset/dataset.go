// Package dataset provides column-based data structures for machine learning datasets.
//
// Architecture:
// - dataset.go: Core Dataset structure for full data storage
// - dataset_view.go: DatasetView for efficient subset operations
// - column.go: Type-specific column implementations (IntColumn, FloatColumn, StringColumn)
// - loader.go: CSV data loading functionality
//
// Design Principles:
// - Column-based storage: Data stored by feature type for efficient access
// - View-based subsets: DatasetView references full dataset without data duplication
// - Type safety: Separate column types with unified FeatureColumn interface
// - Immutable datasets: Once created, datasets don't change (views can be created)
//
// Memory Management:
// - Dataset holds full data in typed columns
// - DatasetView holds only indices and cached calculations
// - No data duplication between views and parent dataset
//
// Example Usage:
//
//	// Create dataset and add columns
//	dataset := NewDataset[string](1000)
//	dataset.AddIntColumn("age", ages, nullMask)
//	dataset.AddFloatColumn("salary", salaries, nullMask)
//
//	// Create view for subset of rows
//	view := dataset.CreateView([]int{0, 2, 4, 6})
//	value, err := view.GetFeatureValue(0, "age") // logical index 0 = physical index 0
//
//	// Create child view from parent view
//	childView := view.CreateChildView([]int{0, 2}) // subset of view's indices
//
// Security: No sensitive data storage, thread-safe for concurrent read access.
// Performance: O(1) column access, O(n) view creation, cached target distributions.
// Dependencies: Requires features package for FeatureInfo and FeatureType definitions.
package dataset

import (
	"fmt"
	"slices"

	"github.com/berrijam/mulberri/internal/data/features"
)

// ====================
// Dataset Structure (Full Data Storage)
// ====================

// Dataset represents a complete machine learning dataset with typed column storage.
//
// Purpose: Stores full dataset with efficient column-based access for ML algorithms.
// The dataset maintains all data in memory using type-specific columns for optimal
// performance during feature access and target retrieval.
//
// Constraints:
// - All columns must have same length (totalSize)
// - Feature names must be unique across all column types
// - Target array length must match column lengths
// - Generic type T must be comparable for target values
//
// Security: No sensitive data validation - assumes pre-validated input.
// Relationships: Referenced by DatasetView instances, contains FeatureInfo metadata.
// Side effects: Memory allocation for column data, maintains feature ordering.
//
// Example:
//
//	dataset := NewDataset[string](1000) // capacity hint for targets
//	err := dataset.AddIntColumn("age", []int64{25, 30, 35}, []bool{false, false, false})
//	dataset.AddTarget("ClassA")
type Dataset[T comparable] struct {
	// Separate storage for each type - FULL DATA
	intColumns    map[string]*IntColumn    // "age", "experience_years"
	floatColumns  map[string]*FloatColumn  // "salary", "gpa", "height"
	stringColumns map[string]*StringColumn // "education", "department", "city"

	targets      []T                              // target values - FULL ARRAY
	featureInfo  map[string]*features.FeatureInfo // metadata
	featureOrder []string                         // consistent feature ordering
	totalSize    int                              // total dataset size
}

// NewDataset creates a new empty dataset with specified target capacity.
//
// Args:
// - capacity: Initial capacity hint for target slice (performance optimization)
//
// Returns: Initialized dataset with empty columns and zero size.
// Constraints: capacity >= 0 (negative values treated as 0).
// Performance: O(1) initialization, pre-allocates target slice for efficiency.
// Side effects: Allocates memory for maps and slices, no data storage yet.
//
// Example: dataset := NewDataset[string](1000) // expects ~1000 target values
func NewDataset[T comparable](capacity int) *Dataset[T] {
	return &Dataset[T]{
		intColumns:    make(map[string]*IntColumn),
		floatColumns:  make(map[string]*FloatColumn),
		stringColumns: make(map[string]*StringColumn),
		targets:       make([]T, 0, capacity),
		featureInfo:   make(map[string]*features.FeatureInfo),
		featureOrder:  make([]string, 0),
		totalSize:     0,
	}
}

// ====================
// Dataset Methods (Full Data Operations)
// ====================

// CreateView creates a DatasetView referencing specified rows from this dataset.
//
// Args:
// - activeIndices: Physical row indices to include in view (must be valid dataset indices)
//
// Returns: New DatasetView with dirty target distribution cache.
// Constraints: All indices must be in range [0, totalSize), duplicates allowed.
// Performance: O(1) view creation, O(n) when target distribution calculated.
// Relationships: View maintains reference to this dataset, shares no data.
// Side effects: Allocates DatasetView structure, marks target cache as dirty.
//
// Example: view := dataset.CreateView([]int{0, 2, 4}) // rows 0, 2, 4 only
func (d *Dataset[T]) CreateView(activeIndices []int) *DatasetView[T] {
	return &DatasetView[T]{
		dataset:         d,
		activeIndices:   activeIndices,
		size:            len(activeIndices),
		targetDistDirty: true,
	}
}

// GetColumn retrieves typed column by feature name with unified interface.
//
// Args:
// - featureName: Name of feature column to retrieve (case-sensitive)
//
// Returns: FeatureColumn interface for type-agnostic access, or error if not found.
// Constraints: featureName must exist in one of the column type maps.
// Performance: O(1) hash map lookups across three column type maps.
// Relationships: Returns interface to actual column data, no copying.
// Side effects: None, read-only operation.
//
// Example: col, err := dataset.GetColumn("age") // returns IntColumn as FeatureColumn
func (d *Dataset[T]) GetColumn(featureName string) (FeatureColumn, error) {
	// Check in all three type maps
	if col, exists := d.intColumns[featureName]; exists {
		return col, nil
	}
	if col, exists := d.floatColumns[featureName]; exists {
		return col, nil
	}
	if col, exists := d.stringColumns[featureName]; exists {
		return col, nil
	}
	return nil, fmt.Errorf("feature %s not found", featureName)
}

// GetTotalSize returns the total number of rows in the dataset
func (d *Dataset[T]) GetTotalSize() int {
	return d.totalSize
}

// GetFeatureOrder returns a copy of the feature order slice
func (d *Dataset[T]) GetFeatureOrder() []string {
	order := make([]string, len(d.featureOrder))
	copy(order, d.featureOrder)
	return order
}

// GetFeatureInfo returns the feature info for a given feature name
func (d *Dataset[T]) GetFeatureInfo(featureName string) (*features.FeatureInfo, error) {
	info, exists := d.featureInfo[featureName]
	if !exists {
		return nil, fmt.Errorf("feature info for %s not found", featureName)
	}
	return info, nil
}

// AddTarget adds a target value to the dataset
func (d *Dataset[T]) AddTarget(target T) {
	d.targets = append(d.targets, target)
}

// GetTarget returns the target value at the specified index
func (d *Dataset[T]) GetTarget(index int) (T, error) {
	if index < 0 || index >= len(d.targets) {
		var zero T
		return zero, fmt.Errorf("target index %d out of bounds [0,%d)", index, len(d.targets))
	}
	return d.targets[index], nil
}

// SetFeatureInfo sets the feature info for a given feature name
func (d *Dataset[T]) SetFeatureInfo(featureName string, info *features.FeatureInfo) {
	d.featureInfo[featureName] = info

	// Add to feature order if not already present
	if slices.Contains(d.featureOrder, featureName) {
		return
	}
	d.featureOrder = append(d.featureOrder, featureName)
}

// AddIntColumn adds an integer column to the dataset
func (d *Dataset[T]) AddIntColumn(name string, data []int64, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.intColumns[name] = &IntColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}

// AddFloatColumn adds a float column to the dataset
func (d *Dataset[T]) AddFloatColumn(name string, data []float64, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.floatColumns[name] = &FloatColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}

// AddStringColumn adds a string column to the dataset
func (d *Dataset[T]) AddStringColumn(name string, data []string, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.stringColumns[name] = &StringColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}
