package dataset

import (
	"fmt"
	"slices"

	"github.com/berrijam/mulberri/internal/data/features"
)

// ====================
// Dataset Structure (Full Data Storage)
// ====================

type Dataset[T comparable] struct {
	// Separate storage for each type - FULL DATA
	intColumns    map[string]*IntColumn    // "age", "experience_years"
	floatColumns  map[string]*FloatColumn  // "salary", "gpa", "height"
	stringColumns map[string]*StringColumn // "education", "department", "city"

	targets      []T                              // target values - FULL ARRAY
	featureInfo  map[string]*features.FeatureInfo // metadata
	featureOrder []string                         // consistent feature ordering
	totalSize    int                              // total dataset size
}

// NewDataset creates a new dataset with the specified initial capacity
func NewDataset[T comparable](capacity int) *Dataset[T] {
	return &Dataset[T]{
		intColumns:    make(map[string]*IntColumn),
		floatColumns:  make(map[string]*FloatColumn),
		stringColumns: make(map[string]*StringColumn),
		targets:       make([]T, 0, capacity),
		featureInfo:   make(map[string]*features.FeatureInfo),
		featureOrder:  make([]string, 0),
		totalSize:     0,
	}
}

// ====================
// Dataset Methods (Full Data Operations)
// ====================

// CreateView creates a view with specified row indices
func (d *Dataset[T]) CreateView(activeIndices []int) *DatasetView[T] {
	return &DatasetView[T]{
		dataset:         d,
		activeIndices:   activeIndices,
		size:            len(activeIndices),
		targetDistDirty: true,
	}
}

// GetColumn returns column interface for any feature type
func (d *Dataset[T]) GetColumn(featureName string) (FeatureColumn, error) {
	// Check in all three type maps
	if col, exists := d.intColumns[featureName]; exists {
		return col, nil
	}
	if col, exists := d.floatColumns[featureName]; exists {
		return col, nil
	}
	if col, exists := d.stringColumns[featureName]; exists {
		return col, nil
	}
	return nil, fmt.Errorf("feature %s not found", featureName)
}

// GetTotalSize returns the total number of rows in the dataset
func (d *Dataset[T]) GetTotalSize() int {
	return d.totalSize
}

// GetFeatureOrder returns a copy of the feature order slice
func (d *Dataset[T]) GetFeatureOrder() []string {
	order := make([]string, len(d.featureOrder))
	copy(order, d.featureOrder)
	return order
}

// GetFeatureInfo returns the feature info for a given feature name
func (d *Dataset[T]) GetFeatureInfo(featureName string) (*features.FeatureInfo, error) {
	info, exists := d.featureInfo[featureName]
	if !exists {
		return nil, fmt.Errorf("feature info for %s not found", featureName)
	}
	return info, nil
}

// AddTarget adds a target value to the dataset
func (d *Dataset[T]) AddTarget(target T) {
	d.targets = append(d.targets, target)
}

// GetTarget returns the target value at the specified index
func (d *Dataset[T]) GetTarget(index int) (T, error) {
	if index < 0 || index >= len(d.targets) {
		var zero T
		return zero, fmt.Errorf("target index %d out of bounds [0,%d)", index, len(d.targets))
	}
	return d.targets[index], nil
}

// SetFeatureInfo sets the feature info for a given feature name
func (d *Dataset[T]) SetFeatureInfo(featureName string, info *features.FeatureInfo) {
	d.featureInfo[featureName] = info

	// Add to feature order if not already present
	if slices.Contains(d.featureOrder, featureName) {
		return
	}
	d.featureOrder = append(d.featureOrder, featureName)
}

// AddIntColumn adds an integer column to the dataset
func (d *Dataset[T]) AddIntColumn(name string, data []int64, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.intColumns[name] = &IntColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}

// AddFloatColumn adds a float column to the dataset
func (d *Dataset[T]) AddFloatColumn(name string, data []float64, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.floatColumns[name] = &FloatColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}

// AddStringColumn adds a string column to the dataset
func (d *Dataset[T]) AddStringColumn(name string, data []string, nullMask []bool) error {
	if len(data) != len(nullMask) {
		return fmt.Errorf("data and null mask lengths don't match for %s", name)
	}

	if d.totalSize == 0 {
		d.totalSize = len(data)
	} else if len(data) != d.totalSize {
		return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
	}

	d.stringColumns[name] = &StringColumn{
		data:     data,
		nullMask: nullMask,
	}

	// Add to feature order if not already present
	if !slices.Contains(d.featureOrder, name) {
		d.featureOrder = append(d.featureOrder, name)
	}

	return nil
}
