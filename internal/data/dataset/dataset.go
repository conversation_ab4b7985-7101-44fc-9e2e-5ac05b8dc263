package dataset

import "github.com/berrijam/mulberri/internal/data/features"

type Dataset[T comparable] struct {
	// Column storage by type - FULL DATA ONLY
	intColumns    map[string]*IntColumn    // "age", "experience_years"
	floatColumns  map[string]*FloatColumn  // "salary", "gpa", "height"
	stringColumns map[string]*StringColumn // "education", "department", "city"

	// Target values - FULL ARRAY
	targets []T

	// Metadata and indexing
	featureInfo  map[string]*features.FeatureInfo // Feature metadata
	featureOrder []string                         // Consistent feature ordering
	totalSize    int                              // total dataset size

}
func NewDataset[T comparable](capacity int) *Dataset[T] {
    return &Dataset[T]{
        intColumns:    make(map[string]*IntColumn),
        floatColumns:  make(map[string]*FloatColumn),
        stringColumns: make(map[string]*StringColumn),
        targets:       make([]T, 0, capacity),
        featureInfo:   make(map[string]*features.FeatureInfo),
        totalSize:     0,
    }
}

func (d *Dataset[T]) AddIntColumn(name string, data []int64, nullMask []bool) error {
    if d.isImmutable {
        return fmt.Errorf("cannot modify immutable dataset")
    }
    
    if len(data) != len(nullMask) {
        return fmt.Errorf("data and null mask lengths don't match for %s", name)
    }
    
    if d.totalSize == 0 {
        d.totalSize = len(data)
    } else if len(data) != d.totalSize {
        return fmt.Errorf("column %s length (%d) doesn't match dataset size (%d)", name, len(data), d.totalSize)
    }
    
    d.intColumns[name] = &IntColumn{
        data:     data,
        nullMask: nullMask,
    }
    d.columnTypes[name] = IntegerFeature
    d.featureOrder = append(d.featureOrder, name)
    
    // Calculate null count
    nullCount := 0
    for _, isNull := range nullMask {
        if isNull {
            nullCount++
        }
    }
    d.nullCounts[name] = nullCount
    
    // Calculate global statistics
    d.calculateGlobalStats(name)
    
    return nil
}