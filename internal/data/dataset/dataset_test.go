package dataset

import (
	"testing"
)

func TestDatasetAndDatasetView(t *testing.T) {
	// Create a new dataset
	dataset := NewDataset[string](10)

	// Add some test data
	ages := []int64{25, 30, 35, 40, 45}
	ageNulls := []bool{false, false, false, false, false}
	err := dataset.AddIntColumn("age", ages, ageNulls)
	if err != nil {
		t.Fatalf("Failed to add age column: %v", err)
	}

	salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
	salaryNulls := []bool{false, false, false, false, false}
	err = dataset.AddFloatColumn("salary", salaries, salaryNulls)
	if err != nil {
		t.Fatalf("Failed to add salary column: %v", err)
	}

	departments := []string{"Engineering", "Sales", "Marketing", "Engineering", "Sales"}
	deptNulls := []bool{false, false, false, false, false}
	err = dataset.AddStringColumn("department", departments, deptNulls)
	if err != nil {
		t.Fatalf("Failed to add department column: %v", err)
	}

	// Add targets
	targets := []string{"A", "B", "A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Test basic dataset functionality
	if dataset.GetTotalSize() != 5 {
		t.Errorf("Expected dataset size 5, got %d", dataset.GetTotalSize())
	}

	// Test getting columns
	ageCol, err := dataset.GetColumn("age")
	if err != nil {
		t.Fatalf("Failed to get age column: %v", err)
	}
	if ageCol.GetSize() != 5 {
		t.Errorf("Expected age column size 5, got %d", ageCol.GetSize())
	}

	// Test getting a value from column
	val, err := ageCol.GetValue(0)
	if err != nil {
		t.Fatalf("Failed to get value from age column: %v", err)
	}
	if val != int64(25) {
		t.Errorf("Expected age value 25, got %v", val)
	}

	// Test creating a view with subset of indices
	viewIndices := []int{0, 2, 4} // indices 0, 2, 4 (ages 25, 35, 45)
	view := dataset.CreateView(viewIndices)

	if view.GetSize() != 3 {
		t.Errorf("Expected view size 3, got %d", view.GetSize())
	}

	// Test getting feature value from view (logical index 0 = physical index 0)
	ageValue, err := view.GetFeatureValue(0, "age")
	if err != nil {
		t.Fatalf("Failed to get age value from view: %v", err)
	}
	if ageValue != int64(25) {
		t.Errorf("Expected age value 25, got %v", ageValue)
	}

	// Test getting feature value from view (logical index 1 = physical index 2)
	ageValue2, err := view.GetFeatureValue(1, "age")
	if err != nil {
		t.Fatalf("Failed to get age value from view: %v", err)
	}
	if ageValue2 != int64(35) {
		t.Errorf("Expected age value 35, got %v", ageValue2)
	}

	// Test getting target from view
	target, err := view.GetTarget(0)
	if err != nil {
		t.Fatalf("Failed to get target from view: %v", err)
	}
	if target != "A" {
		t.Errorf("Expected target 'A', got %v", target)
	}

	// Test target distribution
	dist, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get target distribution: %v", err)
	}
	if dist["A"] != 3 {
		t.Errorf("Expected 3 'A' targets in view, got %d", dist["A"])
	}
	if dist["B"] != 0 {
		t.Errorf("Expected 0 'B' targets in view, got %d", dist["B"])
	}

	// Test creating child view
	childIndices := []int{0, 2} // logical indices in view (physical indices 0, 4)
	childView := view.CreateChildView(childIndices)

	if childView.GetSize() != 2 {
		t.Errorf("Expected child view size 2, got %d", childView.GetSize())
	}

	// Test getting value from child view
	childAge, err := childView.GetFeatureValue(0, "age")
	if err != nil {
		t.Fatalf("Failed to get age from child view: %v", err)
	}
	if childAge != int64(25) {
		t.Errorf("Expected child view age 25, got %v", childAge)
	}

	childAge2, err := childView.GetFeatureValue(1, "age")
	if err != nil {
		t.Fatalf("Failed to get age from child view: %v", err)
	}
	if childAge2 != int64(45) {
		t.Errorf("Expected child view age 45, got %v", childAge2)
	}
}

func TestDatasetViewCaching(t *testing.T) {
	// Create a simple dataset
	dataset := NewDataset[string](3)

	// Add targets
	targets := []string{"A", "B", "A"}
	for _, target := range targets {
		dataset.AddTarget(target)
	}

	// Create view
	view := dataset.CreateView([]int{0, 1, 2})

	// Get distribution first time (should calculate)
	dist1, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get target distribution: %v", err)
	}

	// Get distribution second time (should use cache)
	dist2, err := view.GetTargetDistribution()
	if err != nil {
		t.Fatalf("Failed to get cached target distribution: %v", err)
	}

	// Should be the same
	if len(dist1) != len(dist2) {
		t.Errorf("Cached distribution differs from calculated")
	}
	for k, v := range dist1 {
		if dist2[k] != v {
			t.Errorf("Cached distribution value differs: %s: %d vs %d", k, v, dist2[k])
		}
	}
}
