package dataset

import "fmt"

// ====================
// DatasetView Structure (Subset Operations)
// ====================

// DatasetView represents a subset of the full dataset
type DatasetView[T comparable] struct {
	dataset       *Dataset[T] // Reference to full dataset
	activeIndices []int       // Which rows from full dataset are active
	size          int         // Number of active rows

	// Cached calculations for this view
	targetDist      map[T]int // Cached target distribution
	targetDistDirty bool      // Whether cache needs refresh
}

// ====================
// DatasetView Methods (Subset Operations)
// ====================

// GetFeatureValue gets value for logical index within this view
func (v *DatasetView[T]) GetFeatureValue(logicalIndex int, featureName string) (any, error) {
	if logicalIndex >= v.size {
		return nil, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
	}

	// Convert logical index to physical index in full dataset
	physicalIndex := v.activeIndices[logicalIndex]

	column, err := v.dataset.GetColumn(featureName)
	if err != nil {
		return nil, err
	}

	return column.GetValue(physicalIndex)
}

// GetTarget gets target for logical index within this view
func (v *DatasetView[T]) GetTarget(logicalIndex int) (T, error) {
	if logicalIndex >= v.size {
		var zero T
		return zero, fmt.Errorf("logical index %d out of bounds [0,%d)", logicalIndex, v.size)
	}

	physicalIndex := v.activeIndices[logicalIndex]
	return v.dataset.targets[physicalIndex], nil
}

// GetTargetDistribution calculates distribution for active indices only
func (v *DatasetView[T]) GetTargetDistribution() (map[T]int, error) {
	if !v.targetDistDirty && v.targetDist != nil {
		return v.targetDist, nil // return cached
	}

	distribution := make(map[T]int)

	// Only iterate over active indices
	for _, physicalIndex := range v.activeIndices {
		target := v.dataset.targets[physicalIndex]
		distribution[target]++
	}

	v.targetDist = distribution
	v.targetDistDirty = false
	return distribution, nil
}

// CreateChildView creates a new view from subset of current view's indices
func (v *DatasetView[T]) CreateChildView(logicalIndices []int) *DatasetView[T] {
	// Convert logical indices (within this view) to physical indices (in full dataset)
	physicalIndices := make([]int, len(logicalIndices))
	for i, logicalIndex := range logicalIndices {
		physicalIndices[i] = v.activeIndices[logicalIndex]
	}

	return &DatasetView[T]{
		dataset:         v.dataset, // Same full dataset reference
		activeIndices:   physicalIndices,
		size:            len(physicalIndices),
		targetDistDirty: true,
	}
}

// GetSize returns the number of active rows in this view
func (v *DatasetView[T]) GetSize() int {
	return v.size
}

// GetActiveIndices returns a copy of the active indices for this view
func (v *DatasetView[T]) GetActiveIndices() []int {
	indices := make([]int, len(v.activeIndices))
	copy(indices, v.activeIndices)
	return indices
}
